{extend name="public:layout" /}

{block name="main-container"}
<!-- Breadcrumb -->
<div class="bg-white border-b border-gray-200">
    <div class="container mx-auto px-6 py-3">
        <nav class="flex items-center space-x-2 text-sm text-gray-600">
            <a href="index.html" class="hover:text-blue-600 transition-colors">
                <i class="fas fa-home mr-1"></i>首页
            </a>
            {volist name="breadcrumb" id="v"}
            <i class="fas fa-chevron-right text-gray-400"></i>
            <a class="text-primary-dark" href="{$v.url}" target="{$v.target}">{$v.name}</a>
            {/volist}
        </nav>
    </div>
</div>

<!-- Main Content -->
<main class="container mx-auto px-6 py-8">
    <div class="grid lg:grid-cols-4 gap-6">
        <!-- Left Column - Video Content -->
        <div class="lg:col-span-3">
            <!-- Video Header -->
            <div class="bg-white rounded-xl border border-gray-100 p-8 mb-6">
                <div class="text-center">
                    <h1 class="text-3xl font-bold text-gray-800 mb-4 leading-tight">
                        {$document.title|default=""}
                    </h1>
                    <div class="flex items-center justify-center space-x-6 text-sm text-gray-500">
                        <span><i class="fas fa-calendar mr-2"></i>发布时间：{$document.create_time|time_tran}</span>
                        <span><i class="fas fa-user mr-2"></i>作者：{$document.author|default=''} </span>
                        <span><i class="fas fa-eye mr-2"></i>浏览次数：{$document.view|default=0}</span>
                    </div>
                </div>
            </div>

            <!-- Video Player -->
            <div class="bg-white rounded-xl border border-gray-100 overflow-hidden">
                <div class="p-8 flex justify-center">
                    <div class="video-container w-full">
                        <video id="video-player" class="video-js vjs-big-play-centered" controls preload="auto"
                            poster="{$document.cover|get_file_path}" data-setup='{}'>
                            <source src="{$document.video_url|get_file_path}" type="video/mp4">
                            您的浏览器不支持视频播放。
                        </video>
                    </div>
                    <!-- Video Actions -->
                    <div class="bg-gray-50 px-8 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <button
                                    class="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                                    <i class="fas fa-print"></i>
                                    <span>打印</span>
                                </button>
                            </div>
                            <div class="text-sm text-gray-500">
                                <span>责任编辑：{$document.author|default=''} </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="bg-white rounded-xl border border-gray-100 p-6 mt-6">
                    <div class="grid md:grid-cols-2 gap-4">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-chevron-left text-gray-400"></i>
                            <div>
                                <div class="text-sm text-gray-500">上一篇</div>
                                {if isset($prev.url) && $prev.url}
                                <a href="{$prev.url}"
                                    class="text-blue-600 hover:text-blue-700 font-medium line-clamp-1">
                                    {$prev.title|default='已经没有上一篇了'}
                                </a>
                                {else/}
                                <span class="text-gray-400 font-medium line-clamp-1 cursor-not-allowed">
                                    已经没有上一篇了
                                </span>
                                {/if}
                            </div>
                        </div>
                        <div class="flex items-center justify-end space-x-3">
                            <div class="text-right">
                                <div class="text-sm text-gray-500">下一篇</div>
                                {if isset($next.url) && $next.url}
                                <a href="{$next.url}"
                                    class="text-blue-600 hover:text-blue-700 font-medium line-clamp-1">
                                    {$next.title|default='已经没有下一篇了'}
                                </a>
                                {else/}
                                <span class="text-gray-400 font-medium line-clamp-1 cursor-not-allowed">
                                    已经没有下一篇了
                                </span>
                                {/if}
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                </div>
            </div>

            {include file="public/right" /}
        </div>
</main>

{/block}

{block name="style"}
<link href="https://cdn.jsdelivr.net/npm/video.js@8.10.0/dist/video-js.min.css" rel="stylesheet">
<style>
    /* 视频播放器样式优化 - 修复高度过高和封面图片重复平铺问题 */

    /* 容器限制 - 确保视频播放器容器有合理的尺寸限制 */
    .video-container {
        max-height: 600px;
        width: 100%;
        aspect-ratio: 16/9;
        overflow: hidden;
        position: relative;
        background: #000;
    }

    /* Video.js播放器强制尺寸控制 */
    #video-player.video-js {
        max-height: 600px !important;
        height: auto !important;
        width: 100% !important;
        position: relative !important;
    }

    /* 强制限制Video.js内部技术层的尺寸 */
    #video-player .vjs-tech {
        max-height: 600px !important;
        object-fit: contain !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        width: auto !important;
        height: auto !important;
        max-width: 100% !important;
    }

    /* 优化封面图片显示，避免重复平铺 */
    #video-player .vjs-poster {
        background-size: contain !important;
        background-repeat: no-repeat !important;
        background-position: center center !important;
        max-height: 600px !important;
    }

    /* 为不支持aspect-ratio的浏览器提供降级方案 */
    @supports not (aspect-ratio: 16/9) {
        .video-container {
            height: 400px;
        }

        #video-player.video-js {
            height: 400px !important;
        }
    }

    /* 响应式设计 - 在小屏幕上调整最大高度 */
    @media (max-width: 768px) {
        .video-container {
            max-height: 300px;
        }

        #video-player.video-js {
            max-height: 300px !important;
        }

        #video-player .vjs-tech {
            max-height: 300px !important;
        }

        #video-player .vjs-poster {
            max-height: 300px !important;
        }

        @supports not (aspect-ratio: 16/9) {
            .video-container {
                height: 250px;
            }

            #video-player.video-js {
                height: 250px !important;
            }
        }
    }
</style>
{/block}

{block name="script"}
<script src="https://cdn.jsdelivr.net/npm/video.js@8.10.0/dist/video.min.js"></script>
<script>
    // 可根据需要自定义 Video.js 配置
    var player = videojs('video-player');
</script>
{/block}